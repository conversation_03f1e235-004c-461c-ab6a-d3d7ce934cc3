/**
 * 通用打印数据处理工具
 * 用于处理不同单据类型的打印数据转换
 */

/**
 * 单据类型枚举
 */
export enum WmBillTypeId {
  调拨出库 = 31,
  调出退回 = 32,
  调拨入库 = 41,
  调入退回 = 42
}

/**
 * 业务类型枚举
 */
export enum BsnType {
  采购入库 = 1,
  销售出库 = 2,
  库存损溢 = 3,
  库存调拨 = 4,
  其他入库 = 5
}

/**
 * 打印数据处理接口
 */
export interface PrintRecord {
  wbSeqid?: string | number;
  wmbillTypeId?: number;
  bsnType?: number;
  bsnTypeId?: number;
  deptName?: string;
  applyDeptname?: string;
  sectionName?: string;
  artName?: string;
  [key: string]: any;
}

/**
 * 通用打印数据处理方法
 * @param record 原始单据数据
 * @returns 处理后的打印数据
 */
export const processPrintData = (record: PrintRecord): PrintRecord => {
  // 创建record的副本，避免直接修改原对象
  const printRecord = { ...record };
   const DeptName = printRecord.deptName;
  if (record.sectionName) {
    // 病区相关业务处理
    console.log("病区业务处理", record.sectionName);
    
    switch (record.wmbillTypeId) {
      case WmBillTypeId.调出退回:
        printRecord.deptName =  record.sectionName;
          printRecord.applyDeptname = DeptName;
        // 病区退回药房：调出是病区，调入是药房
        console.log("病区退回药房 - 调出是病区，调入是药房");
        // 可以在这里添加特殊的数据处理逻辑
        // 例如：交换部门信息等
        break;
      case WmBillTypeId.调拨出库:
        // 调拨出库
        console.log("病区调拨出库");
        break;
        
      case WmBillTypeId.调拨入库:
        // 调拨入库
        console.log("病区调拨入库");
        break;
        
      case WmBillTypeId.调入退回:
        // 调入退回
        console.log("病区调入退回");
        break;
        
      default:
        console.log("未知病区业务类型:", record.wmbillTypeId);
        break;
    }
  } else {
    // 非病区业务处理
    if (printRecord.bsnType === BsnType.库存调拨) {
     
      console.log("非病区调拨业务处理", DeptName);
      
      switch (printRecord.wmbillTypeId) {
        case WmBillTypeId.调拨出库:
          // 调拨出库
          console.log("调拨出库");
          // 调拨出库通常不需要交换部门信息
          break;
          
        case WmBillTypeId.调出退回:
          // 调出退回
          console.log("调出退回");
          // 调出退回的处理逻辑
          printRecord.deptName =  record.sectionName;
          printRecord.applyDeptname = DeptName;
          break;
        case WmBillTypeId.调拨入库:
          // 调拨入库：需要交换部门信息
          console.log("调拨入库 - 交换部门信息");
          printRecord.deptName = printRecord.applyDeptname;
          printRecord.applyDeptname = DeptName;
          break;
          
        case WmBillTypeId.调入退回:
          // 调入退回
          
          console.log("调入退回");
          // 调入退回的处理逻辑
          break;
          
        default:
          console.log("未知调拨业务类型:", record.wmbillTypeId);
          break;
      }
    }
  }
  
  return printRecord;
};

/**
 * 获取单据类型描述
 * @param wmbillTypeId 单据类型ID
 * @returns 单据类型描述
 */
export const getBillTypeDescription = (wmbillTypeId: number): string => {
  switch (wmbillTypeId) {
    case WmBillTypeId.调拨出库:
      return "调拨出库";
    case WmBillTypeId.调出退回:
      return "调出退回";
    case WmBillTypeId.调拨入库:
      return "调拨入库";
    case WmBillTypeId.调入退回:
      return "调入退回";
    default:
      return "未知类型";
  }
};

/**
 * 获取业务类型描述
 * @param bsnType 业务类型ID
 * @returns 业务类型描述
 */
export const getBsnTypeDescription = (bsnType: number): string => {
  switch (bsnType) {
    case BsnType.采购入库:
      return "采购入库";
    case BsnType.销售出库:
      return "销售出库";
    case BsnType.库存损溢:
      return "库存损溢";
    case BsnType.库存调拨:
      return "库存调拨";
    case BsnType.其他入库:
      return "其他入库";
    default:
      return "未知类型";
  }
};

/**
 * 判断是否为病区业务
 * @param record 单据数据
 * @returns 是否为病区业务
 */
export const isSectionBusiness = (record: PrintRecord): boolean => {
  return !!record.sectionName;
};

/**
 * 判断是否需要交换部门信息
 * @param record 单据数据
 * @returns 是否需要交换部门信息
 */
export const needSwapDeptInfo = (record: PrintRecord): boolean => {
  // 非病区的调拨入库需要交换部门信息
  return !record.sectionName && 
         record.bsnType === BsnType.库存调拨 && 
         record.wmbillTypeId === WmBillTypeId.调拨入库;
};

export default {
  processPrintData,
  getBillTypeDescription,
  getBsnTypeDescription,
  isSectionBusiness,
  needSwapDeptInfo,
  WmBillTypeId,
  BsnType
};
