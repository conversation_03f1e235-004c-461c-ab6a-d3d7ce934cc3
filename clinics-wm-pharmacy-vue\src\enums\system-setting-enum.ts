/**
 * 系统参数设置
 * OUTPATIENT_RECIPE_PRINT_PRICE 是否开启处方打印金额显示；1 显示 || 0 不显示
 * OUTPATIENT_RECIPE_MERGE_PHARMACIST 是否发药拣药是否合并 1 开启 || 0 不开启
 * WEST_RECIPE_PRINT_NOTES 西药处方打印注意事项
 * TCM_RECIPE_PRINT_NOTES 中药处方打印注意事项
 * OUTPATIENT_RECIPE_PRINTED_FREQ 处方打印频率显示方式 1 显示频率名称 || 0 显示频率代码
 */

export enum SystemSettingEnum {
  'OUTPATIENT_RECIPE_PRINT_PRICE' = 'OUTPATIENT_RECIPE_PRINT_PRICE',
  'OUTPATIENT_RECIPE_MERGE_PHARMACIST' = 'OUTPATIENT_RECIPE_MERGE_PHARMACIST',
  'WEST_RECIPE_PRINT_NOTES' = 'WEST_RECIPE_PRINT_NOTES',
  'TCM_RECIPE_PRINT_NOTES' = 'TCM_RECIPE_PRINT_NOTES',
  'OUTPATIENT_RECIPE_PRINTED_FREQ' = 'OUTPATIENT_RECIPE_PRINTED_FREQ',
  'CW_TRACK_CODE_VALIDATOR_PREFIX7' = 'CW_TRACK_CODE_VALIDATOR_PREFIX7',
  'CW_SCAN_TRACK_CODE_AFTER_SECTION_DELIVER' = 'CW_SCAN_TRACK_CODE_AFTER_SECTION_DELIVER'
}

const systemSettingMap = Object.entries(SystemSettingEnum).reduce((acc, [key, value]: any) => {
  acc[key] = value // 将编码作为键，枚举成员名称作为值
  return acc
}, {} as { [key: number]: keyof typeof SystemSettingEnum })

function getSystemSettingByCode(code: any): string | undefined {
  if (!code) return
  return systemSettingMap[code]
}

function getSystemSettingCodeLs () {
  return Object.keys(systemSettingMap).map((key: any) => systemSettingMap[key])
}

export {
  getSystemSettingByCode,
  getSystemSettingCodeLs
}